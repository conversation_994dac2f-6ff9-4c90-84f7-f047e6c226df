@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}



body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Mobile and tablet placeholder text visibility fixes */
@media (max-width: 1024px) {
  input::placeholder,
  textarea::placeholder {
    color: #6b7280 !important; /* gray-500 for better visibility */
    opacity: 1 !important;
  }

  /* Specific fixes for form inputs */
  input[type="email"]::placeholder,
  input[type="password"]::placeholder,
  input[type="text"]::placeholder {
    color: #4b5563 !important; /* gray-600 for even better contrast */
    opacity: 1 !important;
  }

  /* Dark background form fixes */
  .bg-white input::placeholder {
    color: #6b7280 !important;
  }

  /* Focus state improvements for mobile and tablet */
  input:focus::placeholder,
  textarea:focus::placeholder {
    color: #9ca3af !important; /* gray-400 when focused */
    opacity: 0.7 !important;
  }

  /* Improve text contrast on mobile for prose content - light mode only */
  .prose p,
  .prose li,
  .prose-gray p,
  .prose-gray li {
    color: #374151 !important; /* gray-700 for better mobile readability */
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6,
  .prose-gray h1,
  .prose-gray h2,
  .prose-gray h3,
  .prose-gray h4,
  .prose-gray h5,
  .prose-gray h6 {
    color: #111827 !important; /* gray-900 for headings */
  }

  /* Improve text contrast on mobile for prose content */
  .prose p,
  .prose li,
  .prose-gray p,
  .prose-gray li {
    color: #374151 !important; /* gray-700 for better mobile readability */
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6,
  .prose-gray h1,
  .prose-gray h2,
  .prose-gray h3,
  .prose-gray h4,
  .prose-gray h5,
  .prose-gray h6 {
    color: #111827 !important; /* gray-900 for headings */
  }



.ebook-reader-content li {
  /* List items should inherit color */
  color: inherit !important;
}

/* Ensure zen mode text is visible on mobile devices */
@media (max-width: 768px) {
  .prose-invert p,
  .prose-invert li,
  .prose-invert span,
  .prose-invert div {
    color: #e5e7eb !important; /* Softer gray-200 instead of bright white */
  }

  .prose-invert h1,
  .prose-invert h2,
  .prose-invert h3,
  .prose-invert h4,
  .prose-invert h5,
  .prose-invert h6 {
    color: #f3f4f6 !important; /* Slightly softer than pure white */
  }
}

  /* Ensure button colors are consistent on mobile and tablet */
  .bg-blue-600 {
    background-color: #2563eb !important;
  }

  .bg-purple-600 {
    background-color: #9333ea !important;
  }

  .bg-gray-100 {
    background-color: #f3f4f6 !important;
  }

  .bg-green-600 {
    background-color: #16a34a !important;
  }

  .bg-emerald-600 {
    background-color: #059669 !important;
  }

  /* Improve button text contrast on mobile and tablet */
  button .text-gray-700 {
    color: #374151 !important;
  }

  button .text-green-700 {
    color: #15803d !important;
  }

  /* Force consistent button backgrounds and text colors */
  button.bg-white,
  .bg-white button {
    background-color: #ffffff !important;
    color: #111827 !important;
  }

  /* Ensure outline buttons have proper contrast */
  button[class*="outline"],
  .variant-outline {
    background-color: #ffffff !important;
    color: #111827 !important;
    border-color: #d1d5db !important;
  }

  button[class*="outline"]:hover,
  .variant-outline:hover {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
  }

  /* Force light backgrounds for book cards on mobile and tablet to prevent automatic dark mode */
  .bg-white {
    background-color: #ffffff !important;
  }

  /* Ensure book card content stays light on mobile and tablet */
  [data-testid="card-content"],
  .card-content,
  .book-card-content {
    background-color: #ffffff !important;
    color: #111827 !important;
  }

  /* Specifically target book cards to prevent any dark mode interference */
  .books-page .bg-white,
  .books-page [class*="card"],
  .books-page [class*="Card"] {
    background-color: #ffffff !important;
  }

  /* Ensure text in book cards stays dark */
  .books-page .text-gray-900,
  .books-page .text-gray-600,
  .books-page .text-gray-700 {
    color: #111827 !important;
  }

  /* Prevent any browser-level dark mode interference on book information */
  .books-page * {
    color-scheme: light !important;
  }

  /* Override any potential system dark mode on book cards */
  .books-page [class*="Card"],
  .books-page [class*="card"] {
    background-color: #ffffff !important;
    color: #111827 !important;
    border-color: #e5e7eb !important;
  }

  /* Fix e-reader navigation button contrast issues */
  .ebook-reader button,
  .immersive-reader button,
  [class*="reader"] button {
    background-color: #ffffff !important;
    color: #111827 !important;
    border-color: #d1d5db !important;
  }

  .ebook-reader button:hover,
  .immersive-reader button:hover,
  [class*="reader"] button:hover {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
  }

  /* Specifically fix Previous/Next chapter buttons */
  button[class*="outline"]:disabled {
    background-color: #f9fafb !important;
    color: #9ca3af !important;
    border-color: #e5e7eb !important;
  }
}

/* Global button contrast fixes for all devices to prevent automatic dark mode interference */
@media (prefers-color-scheme: dark) {
  /* Override system dark mode for critical UI elements */
  button:not([class*="dark"]):not(.dark-mode-button) {
    background-color: var(--button-bg, #ffffff) !important;
    color: var(--button-text, #111827) !important;
    border-color: var(--button-border, #d1d5db) !important;
  }

  /* Ensure outline buttons remain visible */
  button[class*="outline"]:not([class*="dark"]) {
    background-color: #ffffff !important;
    color: #111827 !important;
    border-color: #d1d5db !important;
  }

  button[class*="outline"]:not([class*="dark"]):hover {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
  }

  /* Preserve gradient buttons */
  button[class*="gradient"] {
    /* Let gradient buttons keep their styling */
    background: var(--tw-gradient-stops) !important;
  }

  /* Ensure green tip buttons stay visible */
  button[class*="green"] {
    background-color: #16a34a !important;
    color: #ffffff !important;
  }

  button[class*="green"][class*="outline"] {
    background-color: #ffffff !important;
    color: #15803d !important;
    border-color: #16a34a !important;
  }
}

/* Hide navigation when in e-reader mode */
body.ereader-mode nav {
  display: none !important;
}
